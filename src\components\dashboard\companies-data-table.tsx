"use client";

import { useMemo } from "react";
import { Building2, Loader2 } from "lucide-react";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { useCompaniesData } from "@/hooks/use-companies-data";
import { createCompaniesTableColumns } from "./companies-table-columns";
import { SupportedCurrency } from "./currency-selector";

interface CompaniesDataTableProps {
  selectedPortfolios: string[];
  displayCurrency: SupportedCurrency;
}

export function CompaniesDataTable({
  selectedPortfolios,
  displayCurrency,
}: CompaniesDataTableProps) {
  const {
    data: companiesData,
    isLoading,
    error,
  } = useCompaniesData(selectedPortfolios, displayCurrency);

  const columns = useMemo(
    () => createCompaniesTableColumns(displayCurrency),
    [displayCurrency]
  );

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-portavio-orange" />
            <span>Companiile mele</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Se încarcă datele companiilor...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-portavio-orange" />
            <span>Companiile mele</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <div className="text-lg font-medium mb-2">
                Eroare la încărcarea datelor
              </div>
              <div className="text-sm">
                {error instanceof Error ? error.message : "A apărut o eroare neașteptată"}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!companiesData || companiesData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-portavio-orange" />
            <span>Companiile mele</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <div className="text-lg font-medium mb-2">
                Nu există date disponibile
              </div>
              <div className="text-sm">
                {selectedPortfolios.length === 0
                  ? "Selectați un portofoliu pentru a vedea companiile"
                  : "Nu există tranzacții în portofoliile selectate"}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-portavio-orange" />
            <div>
              <span>Companiile mele</span>
              <p className="text-lg text-muted-foreground font-normal">
                ({companiesData.length} companii)
              </p>
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="px-6 pb-6">
          <DataTable
            columns={columns}
            data={companiesData}
            searchPlaceholder="Caută companii..."
            isLoading={isLoading}
          />
        </div>
      </CardContent>
    </Card>
  );
}
